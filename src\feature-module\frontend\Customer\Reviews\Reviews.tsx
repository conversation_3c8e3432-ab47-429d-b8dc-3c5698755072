import { useState, useEffect, useMemo, useCallback } from 'react';
import { FiEdit, FiTrash, FiCheckCircle, FiXCircle, FiAlertCircle, FiStar, FiEye, FiChevronLeft, FiChevronRight, FiChevronsLeft, FiChevronsRight } from 'react-icons/fi';
import { Pagination, Button, Spinner, Card, CardBody, Chip, Select, SelectItem, Tabs, Tab, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { useAuth } from 'react-oidc-context';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import {
  Review,
  getAllReviews,
  createReview,
  updateReview,
  deleteReview,
  CreateReviewData,
  UpdateReviewData
} from '../../../../service/reviewService';
import ReviewImages from '../../../components/ReviewImages/ReviewImages';
import './Reviews.css';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface Notification {
  type: NotificationType;
  message: string;
  id: string;
  duration?: number;
}

const Reviews = () => {
  const auth = useAuth();







  const defaultReviews = useMemo(() => [
    {
      id: 'review-1',
      title: 'Building Construction Services',
      name: 'Jeffrey Adang',
      email: '<EMAIL>',
      userName: 'Jeffrey Adang',
      userEmail: '<EMAIL>',
      isVerified: true,
      date: new Date('2023-08-25T14:30:00').toISOString(),
      createdAt: new Date('2023-08-25T14:30:00').toISOString(),
      rating: 5,
      review:
        'The construction service delivered excellent craftsmanship, completing my home renovation on time with clear communication throughout. The team was professional, punctual, and maintained a clean work environment. I highly recommend their services for any construction needs.',
      images: [],
      imageUrls: [
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
      ],
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
      userProfileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
      serviceId: '101',
      serviceRating: 5,
      qualityRating: 5,
      valueRating: 4,
      communicationRating: 5,
    },
    {
      id: 'review-2',
      title: 'Plumbing Services',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      userName: 'Sarah Johnson',
      userEmail: '<EMAIL>',
      isVerified: true,
      date: new Date('2023-07-15T09:45:00').toISOString(),
      createdAt: new Date('2023-07-15T09:45:00').toISOString(),
      rating: 4,
      review:
        'The plumbing service was efficient and reliable. The technician arrived on time, quickly diagnosed the issue, and provided a fair quote. The work was completed professionally with excellent attention to detail. Very satisfied with the quality of service.',
      images: [],
      imageUrls: [
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
      ],
      profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
      userProfileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
      serviceId: '102',
      serviceRating: 4,
      qualityRating: 4,
      valueRating: 4,
      communicationRating: 4,
    },
  ], []);

  const [reviews, setReviews] = useState<Review[]>([]);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalReviews, setTotalReviews] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [filterRating, setFilterRating] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<string>('show-reviews');
  const [reviewsPerPage, setReviewsPerPage] = useState(3); // Updated to 3 reviews per page as requested

  // View modal state
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedViewReview, setSelectedViewReview] = useState<Review | null>(null);

  // Function to add a notification
  const addNotification = (type: NotificationType, message: string, duration = 3000) => {
    const id = `notification-${Date.now()}`;
    const newNotification: Notification = { type, message, id, duration };
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(notification => notification.id !== id));
    }, duration);
  };

  // Function to handle viewing a review
  const handleViewReview = (review: Review) => {
    setSelectedViewReview(review);
    setShowViewModal(true);
  };

  // Function to close view modal
  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedViewReview(null);
  };

  // Function to handle reviews per page change
  const handleReviewsPerPageChange = (newPerPage: number) => {
    setReviewsPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing per page
    fetchReviews(1); // Fetch first page with new limit
  };

  // Keyboard navigation for pagination
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
        return; // Don't handle keyboard events when user is typing in inputs
      }

      switch (event.key) {
        case 'ArrowLeft':
          if (currentPage > 1) {
            event.preventDefault();
            goToPreviousPage();
          }
          break;
        case 'ArrowRight':
          if (currentPage < totalPages) {
            event.preventDefault();
            goToNextPage();
          }
          break;
        case 'Home':
          if (totalPages > 1) {
            event.preventDefault();
            goToFirstPage();
          }
          break;
        case 'End':
          if (totalPages > 1) {
            event.preventDefault();
            goToLastPage();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentPage, totalPages]);

  // Calculate review statistics
  const reviewStats = useMemo(() => {
    const allReviews = [...reviews, ...defaultReviews];
    const totalCount = allReviews.length;
    const averageRating = totalCount > 0
      ? allReviews.reduce((sum, review) => sum + review.rating, 0) / totalCount
      : 0;

    const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
      rating,
      count: allReviews.filter(review => review.rating === rating).length,
      percentage: totalCount > 0 ? (allReviews.filter(review => review.rating === rating).length / totalCount) * 100 : 0
    }));

    return {
      totalCount,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution
    };
  }, [reviews, defaultReviews]);

  // Filter reviews based on rating
  const filteredReviews = useMemo(() => {
    if (filterRating === 'all') return reviews;
    return reviews.filter(review => review.rating === parseInt(filterRating));
  }, [reviews, filterRating]);

  // Fetch reviews from API
  const fetchReviews = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      // Always fetch all reviews for "Show Reviews" tab
      const response = await getAllReviews({
        page,
        limit: reviewsPerPage // Use dynamic reviews per page
      });

      // Debug: Log the actual review structure to see what properties are available
      console.log('Fetched reviews response:', response);
      if (response.reviews && response.reviews.length > 0) {
        console.log('First review structure:', response.reviews[0]);
        console.log('Review ID property:', response.reviews[0].id || response.reviews[0]._id);
      }

      setReviews(response.reviews || []);
      setTotalReviews(response.total || 0);
      setTotalPages(response.totalPages || 0);
      setCurrentPage(response.page || 1);

    } catch (error: any) {
      console.error('Error fetching reviews:', error);
      setError('Failed to load reviews. Please try again.');
      addNotification('error', 'Failed to load reviews. Please try again.');

      // Fallback to empty array
      setReviews([]);
      setTotalReviews(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [reviewsPerPage]);

  // Load reviews when component mounts or tab changes
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchReviews(1); // Reset to page 1 when tab changes
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchReviews, activeTab]);

  // Load reviews when page changes (but not tab)
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading && activeTab === 'show-reviews') {
      fetchReviews(currentPage);
    }
  }, [currentPage, auth.isAuthenticated, auth.isLoading, activeTab, fetchReviews]);

  // For server-side pagination, we use all reviews from the current page
  const currentReviews = reviews;

  // Change page - now fetches new data from API
  const handlePageChange = useCallback((page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      fetchReviews(page);
      // Scroll to top when changing pages
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentPage, totalPages, fetchReviews]);

  // Pagination helper functions
  const goToFirstPage = useCallback(() => handlePageChange(1), [handlePageChange]);
  const goToLastPage = useCallback(() => handlePageChange(totalPages), [handlePageChange, totalPages]);
  const goToPreviousPage = useCallback(() => handlePageChange(currentPage - 1), [handlePageChange, currentPage]);
  const goToNextPage = useCallback(() => handlePageChange(currentPage + 1), [handlePageChange, currentPage]);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than or equal to max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages with ellipsis
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      let endPage = Math.min(totalPages, currentPage + halfVisible);

      // Adjust if we're near the beginning or end
      if (currentPage <= halfVisible) {
        endPage = Math.min(totalPages, maxVisiblePages);
      } else if (currentPage > totalPages - halfVisible) {
        startPage = Math.max(1, totalPages - maxVisiblePages + 1);
      }

      // Add first page and ellipsis if needed
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      // Add visible pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis and last page if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handleEditReview = (review: Review) => {
    // Ensure the review has a valid ID
    const reviewId = review.id || review._id;
    if (!reviewId) {
      console.error('Cannot edit review: No valid ID found', review);
      addNotification('error', 'Cannot edit review: Invalid review ID');
      return;
    }

    setSelectedReview(review);
    setIsEditMode(true);
    setShowReviewForm(true);
    addNotification('info', `Editing your review for "${review.title}". Make your changes and click "Update Review".`);
  };



  const handleSubmitReview = async (reviewData: ReviewData) => {
    try {
      setLoading(true);
      addNotification('info', 'Processing your review submission...');

      if (isEditMode && selectedReview) {
        // Update existing review
        const updateData: UpdateReviewData = {
          title: reviewData.title,
          review: reviewData.review,
          rating: reviewData.rating,
          imageUrls: reviewData.imageUrls,
          imageNames: reviewData.imageNames || [], // Include image names for backend storage
        };

        // Only include rating fields that have valid values (1-4)
        if (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4) {
          updateData.serviceRating = Math.round(reviewData.serviceRating);
        }
        if (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4) {
          updateData.qualityRating = Math.round(reviewData.qualityRating);
        }
        if (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4) {
          updateData.valueRating = Math.round(reviewData.valueRating);
        }
        if (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4) {
          updateData.communicationRating = Math.round(reviewData.communicationRating);
        }

        // Backend requires timelinessRating - use overall rating as default if not provided
        if (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4) {
          updateData.timelinessRating = Math.round(reviewData.timelinessRating);
        } else {
          updateData.timelinessRating = Math.round(reviewData.rating);
        }

        // Get the review ID (handle both id and _id)
        const reviewId = selectedReview.id || selectedReview._id;
        if (!reviewId) {
          throw new Error('Cannot update review: Invalid review ID');
        }

        console.log('Updating review with data:', updateData);
        await updateReview(reviewId, updateData);

        // Refresh the current page to show updated review
        await fetchReviews(currentPage);

        // Show success notification
        addNotification('success', `Your review for "${reviewData.title}" has been updated successfully with ${updateData.imageNames?.length || 0} images.`);
      } else {
        // Add new review with user information
        const currentDate = new Date().toISOString();

        // Validate required fields before creating review
        if (!reviewData.serviceId || reviewData.serviceId.trim() === '') {
          addNotification('error', 'Service ID is required to submit a review.');
          return;
        }
        if (!reviewData.rating || reviewData.rating < 1 || reviewData.rating > 4) {
          addNotification('error', 'Please provide a valid rating between 1 and 4.');
          return;
        }
        if (!reviewData.title || reviewData.title.trim() === '') {
          addNotification('error', 'Please provide a review title.');
          return;
        }
        if (!reviewData.review || reviewData.review.trim() === '') {
          addNotification('error', 'Please write your review.');
          return;
        }
        if (reviewData.review.trim().length < 10 || reviewData.review.trim().length > 2000) {
          addNotification('error', 'Review must be between 10 and 2000 characters.');
          return;
        }

        // Log the review data for debugging
        console.log('Creating review with data:', {
          ...reviewData,
          imageCount: reviewData.images?.length || 0,
          imageNamesCount: reviewData.imageNames?.length || 0,
          imageUrlsCount: reviewData.imageUrls?.length || 0
        });

        const createData: CreateReviewData = {
          providerId: reviewData.providerId || 'default-provider',
          serviceId: reviewData.serviceId.trim(),
          serviceName: reviewData.serviceName || 'Service Review',
          bookingId: reviewData.bookingId || 'booking-' + Date.now(), // Generate a booking ID for demo
          title: reviewData.title.trim(),
          review: reviewData.review.trim(),
          comment: reviewData.review.trim(), // Backend expects comment field
          rating: Math.round(reviewData.rating), // Ensure integer rating
          images: reviewData.images || [], // Original File objects (for reference)
          imageUrls: reviewData.imageUrls || [], // S3 URLs (for display)
          imageNames: reviewData.imageNames || [], // S3 image names (for backend storage)
          // Add user information from auth context
          userName: (auth.user as any)?.name || (auth.user as any)?.email || 'User',
          userEmail: (auth.user as any)?.email || '',
          userProfileImage: (auth.user as any)?.picture || '',
          isVerified: (auth.user as any)?.email_verified || false,
          // Add current timestamp
          date: currentDate,
          createdAt: currentDate,
        };

        console.log('Final create data being sent to backend:', {
          ...createData,
          imageNamesArray: createData.imageNames,
          imageNamesCount: createData.imageNames.length
        });

        // Include rating fields - backend requires all ratings including timelinessRating
        if (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4) {
          createData.serviceRating = Math.round(reviewData.serviceRating);
        }
        if (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4) {
          createData.qualityRating = Math.round(reviewData.qualityRating);
        }
        if (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4) {
          createData.valueRating = Math.round(reviewData.valueRating);
        }
        if (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4) {
          createData.communicationRating = Math.round(reviewData.communicationRating);
        }

        // Backend requires timelinessRating - use a default value if not provided
        if (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4) {
          createData.timelinessRating = Math.round(reviewData.timelinessRating);
        } else {
          // Set default timelinessRating to match the overall rating if not provided
          createData.timelinessRating = Math.round(reviewData.rating);
        }

        console.log('Submitting review to backend...');
        const createdReview = await createReview(createData);
        console.log('Review created successfully:', createdReview);

        // Refresh reviews to show the new one
        await fetchReviews(1); // Go to first page to see new review

        // Switch to show-reviews tab to display the newly submitted review
        setActiveTab('show-reviews');

        // Show enhanced success notification with image info
        const imageCount = createData.imageNames.length;
        const successMessage = imageCount > 0
          ? `Your review for "${reviewData.title}" has been submitted successfully with ${imageCount} image${imageCount > 1 ? 's' : ''} uploaded to S3!`
          : `Your review for "${reviewData.title}" has been submitted successfully.`;

        addNotification('success', successMessage);
      }
    } catch (error: any) {
      console.error('Error submitting review:', error);

      // Enhanced error handling for different types of failures
      let errorMessage = 'Failed to submit review. Please try again.';

      if (error.response?.status === 422) {
        // Validation error from backend
        const validationErrors = error.response?.data?.formattedErrors || [];
        if (validationErrors.length > 0) {
          const errorMessages = validationErrors.map((err: any) => err.message).join(', ');
          errorMessage = `Validation error: ${errorMessages}`;
        } else {
          errorMessage = 'Please check your review data and try again.';
        }
      } else if (error.response?.status === 413) {
        // File too large
        errorMessage = 'One or more images are too large. Please use smaller images (max 5MB each).';
      } else if (error.message?.includes('S3') || error.message?.includes('upload')) {
        // S3 upload specific error
        errorMessage = 'Failed to upload images to S3. Please check your images and try again.';
      } else if (error.response?.data?.message) {
        // Backend provided specific error message
        errorMessage = error.response.data.message;
      }

      addNotification('error', errorMessage);
    } finally {
      setLoading(false);

      // Reset form state
      setShowReviewForm(false);
      setSelectedReview(null);
      setIsEditMode(false);
    }
  };

  // Handle delete review with confirmation
  const handleDeleteReviewWithConfirmation = async (reviewId: string, reviewTitle: string) => {
    // Debug: Log the review ID to see what we're getting
    console.log('Delete review called with ID:', reviewId, 'Title:', reviewTitle);

    if (!reviewId || reviewId === 'undefined') {
      console.error('Invalid review ID:', reviewId);
      addNotification('error', 'Cannot delete review: Invalid review ID');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to delete the review "${reviewTitle}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setLoading(true);
      setError(null);

      await deleteReview(reviewId);
      addNotification('success', 'Review deleted successfully!');

      // Refresh reviews list
      await fetchReviews(currentPage);
    } catch (error: any) {
      console.error('Error deleting review:', error);
      addNotification('error', error.response?.data?.message || 'Failed to delete review');
    } finally {
      setLoading(false);
    }
  };



  // Notification component
  const NotificationComponent = ({ notification }: { notification: Notification }) => {
    const { type, message, id } = notification;

    // Define icon and colors based on notification type
    const getNotificationStyles = () => {
      switch (type) {
        case 'success':
          return {
            bgColor: 'bg-green-100',
            textColor: 'text-green-800',
            borderColor: 'border-green-200',
            icon: <FiCheckCircle className="w-5 h-5 text-green-500" />
          };
        case 'error':
          return {
            bgColor: 'bg-red-100',
            textColor: 'text-red-800',
            borderColor: 'border-red-200',
            icon: <FiXCircle className="w-5 h-5 text-red-500" />
          };
        case 'warning':
          return {
            bgColor: 'bg-yellow-100',
            textColor: 'text-yellow-800',
            borderColor: 'border-yellow-200',
            icon: <FiAlertCircle className="w-5 h-5 text-yellow-500" />
          };
        case 'info':
        default:
          return {
            bgColor: 'bg-blue-100',
            textColor: 'text-blue-800',
            borderColor: 'border-blue-200',
            icon: <FiAlertCircle className="w-5 h-5 text-blue-500" />
          };
      }
    };

    const styles = getNotificationStyles();

    return (
      <div
        key={id}
        className={`flex items-center p-4 mb-3 rounded-lg border ${styles.bgColor} ${styles.textColor} ${styles.borderColor} animate-fadeIn`}
        role="alert"
      >
        <div className="mr-2">{styles.icon}</div>
        <div className="text-sm font-medium">{message}</div>
        <button
          type="button"
          className="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 hover:bg-gray-200 focus:outline-none"
          onClick={() => setNotifications(prev => prev.filter(n => n.id !== id))}
          aria-label="Close"
        >
          <FiXCircle className="w-5 h-5" />
        </button>
      </div>
    );
  };

  return (
    <div className="mx-auto p-6 bg-gray-50 min-h-screen relative">
      {/* Notifications container */}
      <div className="fixed top-4 right-4 z-50 w-80 max-w-full">
        {notifications.map(notification => (
          <NotificationComponent key={notification.id} notification={notification} />
        ))}
      </div>

      <BreadCrumb title="Reviews Dashboard" item1="Customer" />
{/* 
      User Profile Header
      {auth.isAuthenticated && auth.user && (
        <div className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img
                src={(auth.user as any)?.picture || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face'}
                alt={`${(auth.user as any)?.name || 'User'}'s profile`}
                className="w-16 h-16 rounded-full border-3 border-white shadow-lg object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face';
                }}
              />
              {(auth.user as any)?.email_verified && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-1">
                <h2 className="text-xl font-semibold text-gray-900">
                  {(auth.user as any)?.name || (auth.user as any)?.email || 'User'}
                </h2>
                {(auth.user as any)?.email_verified && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verified User
                  </span>
                )}
              </div>
              <p className="text-gray-600 text-sm">
                {(auth.user as any)?.email}
              </p>
            </div>
          </div>
        </div>
      )} */}

      {/* Dashboard Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">Reviews Dashboard</h2>
          <p className="text-gray-600">Manage and view all your service reviews</p>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="mb-6">
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={(key) => setActiveTab(key as string)}
          color="primary"
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
            tabContent: "group-data-[selected=true]:text-primary"
          }}
        >
           <Tab
            key="submit-review"
            title={
              <div className="flex items-center space-x-2">
                <FiEdit />
                <span>Submit Review</span>
              </div>
            }
          />
          <Tab
            key="show-reviews"
            title={
              <div className="flex items-center space-x-2">
                <FiEye />
                <span>Show Reviews</span>
              </div>
            }
          />
        </Tabs>
      </div>

      {/* Tab Content */}
      {activeTab === 'submit-review' && (
        <div className="space-y-6">
          {/* Submit Review Form */}
          <Card className="bg-white shadow-lg">
            <CardBody className="p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Submit a New Review</h3>
                <p className="text-gray-600">Share your experience with our services and help others make informed decisions</p>
              </div>

              {/* Review Form Preview */}
              <div className="max-w-4xl mx-auto">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column - Form Preview */}
                  <div className="space-y-6">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <FiEdit className="mr-2 text-blue-600" />
                        What You'll Share
                      </h4>
                      <div className="space-y-3 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FiStar className="mr-2 text-yellow-500" />
                          <span>Rate your overall experience (1-5 stars)</span>
                        </div>
                        <div className="flex items-center">
                          <FiEdit className="mr-2 text-blue-500" />
                          <span>Write a descriptive title for your review</span>
                        </div>
                        <div className="flex items-center">
                          <FiCheckCircle className="mr-2 text-green-500" />
                          <span>Share detailed feedback about the service</span>
                        </div>
                        <div className="flex items-center">
                          <FiEye className="mr-2 text-purple-500" />
                          <span>Add photos (max 5 images) - automatically uploaded to S3</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg border border-green-100">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <FiCheckCircle className="mr-2 text-green-600" />
                        Why Your Review Matters
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Helps other customers make informed decisions</li>
                        <li>• Provides valuable feedback to service providers</li>
                        <li>• Builds trust within our community</li>
                        <li>• Improves overall service quality</li>
                      </ul>
                    </div>
                  </div>

                  {/* Right Column - Action Button */}
                  <div className="flex flex-col justify-center">
                    <div className="text-center mb-6">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                        <FiEdit className="w-8 h-8 text-blue-600" />
                      </div>
                      <h4 className="text-xl font-semibold text-gray-800 mb-2">Ready to Share?</h4>
                      <p className="text-gray-600 mb-6">Your feedback takes just a few minutes and makes a big difference</p>
                    </div>

                    <Button
                      color="primary"
                      size="lg"
                      className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                      startContent={<FiEdit className="w-5 h-5" />}
                      onPress={() => {
                        setIsEditMode(false);
                        setSelectedReview(null);
                        setShowReviewForm(true);
                        addNotification('info', 'Ready to write a review! Please fill out the form to share your experience.');
                      }}
                      isDisabled={loading}
                    >
                      {loading ? 'Processing...' : 'Create Review & Upload Photos'}
                    </Button>

                    <div className="mt-4 text-center text-sm text-gray-500">
                      <p>✨ Your review will be visible to other customers</p>
                      <p className="mt-1">📝 Takes about 2-3 minutes to complete</p>
                      <p className="mt-1">📸 Photos are automatically uploaded to S3 cloud storage</p>
                      <p className="mt-1">🔒 Maximum 5 images per review (5MB each)</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'show-reviews' && (
        <div>
          {/* Show Reviews Tab Content */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-800">All Reviews</h3>
              <p className="text-gray-600 text-sm mt-1">View all reviews from customers</p>
            </div>
          </div>

      {/* Review Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">Total Reviews</p>
                <p className="text-3xl font-bold">{reviewStats.totalCount}</p>
              </div>
              <div className="bg-blue-400 bg-opacity-30 p-3 rounded-full">
                <FiStar className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">Average Rating</p>
                <p className="text-3xl font-bold">{reviewStats.averageRating}</p>
              </div>
              <div className="bg-green-400 bg-opacity-30 p-3 rounded-full">
                <FiCheckCircle className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">5-Star Reviews</p>
                <p className="text-3xl font-bold">
                  {reviewStats.ratingDistribution.find(r => r.rating === 5)?.count || 0}
                </p>
              </div>
              <div className="bg-purple-400 bg-opacity-30 p-3 rounded-full">
                <FiStar className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">This Month</p>
                <p className="text-3xl font-bold">
                  {reviews.filter(r => new Date(r.date).getMonth() === new Date().getMonth()).length}
                </p>
              </div>
              <div className="bg-orange-400 bg-opacity-30 p-3 rounded-full">
                <FiAlertCircle className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card> */}
      </div>

      {/* Filters and Controls */}
      <Card className="mb-6">
        <CardBody className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-gray-800">Filter Reviews</h3>
              <Select
                placeholder="Filter by rating"
                className="w-48"
                selectedKeys={[filterRating]}
                onSelectionChange={(keys) => setFilterRating(Array.from(keys)[0] as string)}
              >
                <SelectItem key="all" value="all">All Ratings</SelectItem>
                <SelectItem key="5" value="5">5 Stars</SelectItem>
                <SelectItem key="4" value="4">4 Stars</SelectItem>
                <SelectItem key="3" value="3">3 Stars</SelectItem>
                <SelectItem key="2" value="2">2 Stars</SelectItem>
                <SelectItem key="1" value="1">1 Star</SelectItem>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant={viewMode === 'grid' ? 'solid' : 'bordered'}
                color="primary"
                onPress={() => setViewMode('grid')}
              >
                Grid View
              </Button>
              <Button
                size="sm"
                variant={viewMode === 'list' ? 'solid' : 'bordered'}
                color="primary"
                onPress={() => setViewMode('list')}
              >
                List View
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Loading State */}
      {loading && (
        <div className="bg-white p-8 rounded-xl shadow-md text-center">
          <Spinner size="lg" color="primary" />
          <p className="text-gray-600 mt-4">Loading reviews...</p>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="bg-white p-8 rounded-xl shadow-md text-center">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Reviews</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button
            color="primary"
            onPress={() => fetchReviews(currentPage)}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* No Reviews State */}
      {!loading && !error && filteredReviews.length === 0 && (
        <Card className="text-center">
          <CardBody className="p-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiStar className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-700 mb-3">
              {filterRating === 'all' ? 'No Reviews Available' : `No ${filterRating}-Star Reviews`}
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              {filterRating === 'all'
                ? "No reviews have been submitted yet. Be the first to share your experience!"
                : `No ${filterRating}-star reviews found. Try adjusting your filter to see other reviews.`
              }
            </p>
            <div className="flex gap-3 justify-center">
              {filterRating !== 'all' && (
                <Button
                  color="secondary"
                  variant="bordered"
                  onPress={() => setFilterRating('all')}
                >
                  Show All Reviews
                </Button>
              )}

              {filterRating === 'all' && (
                <Button
                  color="primary"
                  startContent={<FiEdit />}
                  onPress={() => {
                    setActiveTab('submit-review');
                    addNotification('info', 'Ready to write your first review! Please fill out the form to share your experience.');
                  }}
                >
                  Submit Your First Review
                </Button>
              )}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Reviews List */}
      {!loading && !error && filteredReviews.length > 0 && (
        <div className="space-y-6 mt-4">
          {/* Display showing X of Y reviews */}
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-600">
              Showing {((currentPage - 1) * reviewsPerPage) + 1}-{Math.min(currentPage * reviewsPerPage, totalReviews)} of {totalReviews} reviews
              {filterRating !== 'all' && (
                <Chip size="sm" color="primary" className="ml-2">
                  {filterRating} Star{filterRating !== '1' ? 's' : ''} Only
                </Chip>
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{viewMode === 'grid' ? 'Grid View' : 'List View'}</span>
              <div className="flex items-center gap-2">
                <span>Show:</span>
                <select
                  value={reviewsPerPage}
                  onChange={(e) => handleReviewsPerPageChange(parseInt(e.target.value))}
                  disabled={loading}
                  className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={3}>3 per page</option>
                  <option value={6}>6 per page</option>
                  <option value={9}>9 per page</option>
                  <option value={12}>12 per page</option>
                </select>
              </div>
            </div>
          </div>

          <div className={viewMode === 'grid' ? 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-6'}>
            {currentReviews.map((review) => (
              <Card
                key={review.id}
                className="review-card hover:shadow-lg transition-all duration-300"
              >
                <CardBody className="p-6">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="relative">
                      <img
                        src={
                          review.userProfileImage ||
                          review.profileImage ||
                          (auth.user as any)?.picture ||
                          'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face'
                        }
                        alt={`${review.userName || review.name || (auth.user as any)?.name || 'User'}'s profile`}
                        className="w-12 h-12 rounded-full border-2 border-gray-200 object-cover shadow-sm"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face';
                        }}
                      />
                      {/* Verified user indicator */}
                      {(review.isVerified || (auth.user as any)?.email_verified) && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {review.title}
                      </h3>
                      <div className="flex flex-col space-y-1 text-gray-600 text-sm mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-800">
                            {review.userName || review.name || (auth.user as any)?.name || (auth.user as any)?.email || 'User'}
                          </span>
                          {(review.isVerified || (auth.user as any)?.email_verified) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                              Verified
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span>Submitted on</span>
                          <span className="font-medium text-gray-700">
                            {new Date(review.createdAt || review.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                          <span>•</span>
                          <span className="text-gray-600">
                            {new Date(review.createdAt || review.date).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          {review.updatedAt && review.updatedAt !== (review.createdAt || review.date) && (
                            <>
                              <span>•</span>
                              <span className="text-orange-600 font-medium">Edited</span>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {Array(4)
                          .fill(0)
                          .map((_, i) => (
                            <FiStar
                              key={i}
                              className={`w-4 h-4 ${
                                i < review.rating
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        <span className="ml-2 text-sm font-medium text-gray-700">
                          {review.rating} out of 4
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-gray-700 mb-4">
                    <p className="line-clamp-3">
                      {review.review}
                    </p>
                    {review.review && review.review.length > 150 && (
                      <button
                        onClick={() => handleViewReview(review)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium mt-1 inline-flex items-center gap-1"
                      >
                        Read more <FiEye size={14} />
                      </button>
                    )}
                  </div>

                  {/* Display review images using the reusable component */}
                  <ReviewImages
                    imageNames={review.imageNames}
                    imageUrls={review.imageUrls}
                    maxDisplay={3}
                    size="md"
                    className="mb-4"
                    showCount={true}
                    folderName="review-images"
                  />

                  <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                    <Chip
                      size="sm"
                      color={review.rating >= 4 ? 'success' : review.rating >= 3 ? 'warning' : 'danger'}
                      variant="flat"
                    >
                      {review.rating >= 4 ? 'Excellent' : review.rating >= 3 ? 'Good' : 'Needs Improvement'}
                    </Chip>
                    <div className="flex space-x-3">
                      <Button
                        isIconOnly
                        size="sm"
                        variant="light"
                        color="secondary"
                        onPress={() => {
                          console.log('View button clicked for review:', review);
                          if (!loading) {
                            handleViewReview(review);
                          }
                        }}
                        isDisabled={loading}
                        title="View Full Review"
                        className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                      >
                        <FiEye size={16} />
                      </Button>
                      <Button
                        isIconOnly
                        size="sm"
                        variant="light"
                        color="primary"
                        onPress={() => {
                          // Debug: Log the review object for edit
                          console.log('Edit button clicked for review:', review);
                          if (!loading) {
                            handleEditReview(review);
                          }
                        }}
                        isDisabled={loading}
                        title="Edit Review"
                      >
                        <FiEdit size={16} />
                      </Button>
                      <Button
                        isIconOnly
                        size="sm"
                        variant="light"
                        color="danger"
                        onPress={() => {
                          // Debug: Log the review object to see its structure
                          console.log('Delete button clicked for review:', review);
                          console.log('Review ID:', review.id);
                          console.log('Review _id:', review._id);

                          // Use _id if id is not available (MongoDB default)
                          const reviewId = review.id || review._id;
                          if (!loading && reviewId) {
                            handleDeleteReviewWithConfirmation(reviewId, review.title);
                          } else {
                            console.error('No valid review ID found:', { id: review.id, _id: review._id });
                            addNotification('error', 'Cannot delete review: Invalid review ID');
                          }
                        }}
                        isDisabled={loading}
                        title="Delete Review"
                      >
                        <FiTrash size={16} />
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Enhanced Pagination Controls */}
      {totalPages > 1 && (
        <div className="mt-8 space-y-4">
          {/* Pagination Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Previous/Next Navigation */}
            <div className="flex items-center gap-2">
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToFirstPage}
                isDisabled={loading || currentPage === 1}
                title="First Page"
              >
                <FiChevronsLeft size={16} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToPreviousPage}
                isDisabled={loading || currentPage === 1}
                title="Previous Page"
              >
                <FiChevronLeft size={16} />
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1 mx-2">
                {getPageNumbers().map((page, index) => (
                  <div key={index}>
                    {page === '...' ? (
                      <span className="px-2 py-1 text-gray-500">...</span>
                    ) : (
                      <Button
                        size="sm"
                        variant={currentPage === page ? "solid" : "bordered"}
                        color={currentPage === page ? "primary" : "default"}
                        onPress={() => handlePageChange(page as number)}
                        isDisabled={loading}
                        className={`min-w-[40px] ${
                          currentPage === page
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'hover:bg-blue-50 hover:border-blue-300'
                        }`}
                      >
                        {page}
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToNextPage}
                isDisabled={loading || currentPage === totalPages}
                title="Next Page"
              >
                <FiChevronRight size={16} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToLastPage}
                isDisabled={loading || currentPage === totalPages}
                title="Last Page"
              >
                <FiChevronsRight size={16} />
              </Button>
            </div>

            {/* Page Info */}
            <div className="text-sm text-gray-600 flex items-center gap-4">
              <span>
                Showing {((currentPage - 1) * reviewsPerPage) + 1}-{Math.min(currentPage * reviewsPerPage, totalReviews)} of {totalReviews} reviews
              </span>
              <span className="hidden sm:inline">•</span>
              <span className="hidden sm:inline">
                Page {currentPage} of {totalPages}
              </span>
            </div>
          </div>

          {/* Alternative: HeroUI Pagination Component (for comparison) */}
          <div className="flex justify-center">
            <Pagination
              initialPage={1}
              page={currentPage}
              onChange={handlePageChange}
              total={totalPages}
              isDisabled={loading || totalPages <= 1}
              showControls
              color="primary"
              className="gap-2"
              size="sm"
            />
          </div>

          {/* Quick Page Jump */}
          {totalPages > 10 && (
            <div className="flex justify-center items-center gap-2 text-sm">
              <span className="text-gray-600">Go to page:</span>
              <select
                value={currentPage}
                onChange={(e) => handlePageChange(parseInt(e.target.value))}
                disabled={loading}
                className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <option key={page} value={page}>
                    {page}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Keyboard Navigation Hint */}
          {totalPages > 1 && (
            <div className="text-center text-xs text-gray-500 mt-2">
              💡 Use arrow keys (← →) to navigate pages, or Home/End for first/last page
            </div>
          )}
        </div>
      )}
        </div>
      )}

      {/* View Review Modal */}
      {showViewModal && selectedViewReview && (
        <Modal
          isOpen={showViewModal}
          onClose={handleCloseViewModal}
          size="2xl"
          scrollBehavior="inside"
          classNames={{
            base: "max-h-[90vh]",
            body: "py-6",
            header: "border-b border-gray-200",
            footer: "border-t border-gray-200"
          }}
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-bold text-gray-900">{selectedViewReview.title}</h2>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="font-medium">
                  {selectedViewReview.userName || selectedViewReview.name || 'User'}
                </span>
                {selectedViewReview.isVerified && (
                  <Chip size="sm" color="primary" variant="flat">
                    Verified
                  </Chip>
                )}
                <span>•</span>
                <span>
                  {new Date(selectedViewReview.createdAt || selectedViewReview.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-6">
                {/* User Profile Section */}
                <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                  <img
                    src={
                      selectedViewReview.userProfileImage ||
                      selectedViewReview.profileImage ||
                      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face'
                    }
                    alt={`${selectedViewReview.userName || selectedViewReview.name || 'User'}'s profile`}
                    className="w-16 h-16 rounded-full border-2 border-gray-200 object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face';
                    }}
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      {selectedViewReview.userName || selectedViewReview.name || 'User'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {selectedViewReview.userEmail || selectedViewReview.email || 'No email provided'}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      {Array(4)
                        .fill(0)
                        .map((_, i) => (
                          <FiStar
                            key={i}
                            className={`w-4 h-4 ${
                              i < selectedViewReview.rating
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      <span className="ml-2 text-sm font-medium text-gray-700">
                        {selectedViewReview.rating} out of 4
                      </span>
                    </div>
                  </div>
                </div>

                {/* Review Content */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Review Comment:</h4>
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {selectedViewReview.review || selectedViewReview.comment || 'No comment provided'}
                    </p>
                  </div>

                  {/* Detailed Ratings */}
                  {(selectedViewReview.serviceRating || selectedViewReview.qualityRating ||
                    selectedViewReview.valueRating || selectedViewReview.communicationRating ||
                    selectedViewReview.timelinessRating) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Detailed Ratings:</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {selectedViewReview.serviceRating && (
                          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm font-medium text-gray-700">Service Quality</span>
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < selectedViewReview.serviceRating!
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-1 text-sm text-gray-600">
                                {selectedViewReview.serviceRating}/4
                              </span>
                            </div>
                          </div>
                        )}
                        {selectedViewReview.qualityRating && (
                          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm font-medium text-gray-700">Work Quality</span>
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < selectedViewReview.qualityRating!
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-1 text-sm text-gray-600">
                                {selectedViewReview.qualityRating}/4
                              </span>
                            </div>
                          </div>
                        )}
                        {selectedViewReview.valueRating && (
                          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm font-medium text-gray-700">Value for Money</span>
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < selectedViewReview.valueRating!
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-1 text-sm text-gray-600">
                                {selectedViewReview.valueRating}/4
                              </span>
                            </div>
                          </div>
                        )}
                        {selectedViewReview.communicationRating && (
                          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm font-medium text-gray-700">Communication</span>
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < selectedViewReview.communicationRating!
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-1 text-sm text-gray-600">
                                {selectedViewReview.communicationRating}/4
                              </span>
                            </div>
                          </div>
                        )}
                        {selectedViewReview.timelinessRating && (
                          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm font-medium text-gray-700">Timeliness</span>
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < selectedViewReview.timelinessRating!
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-1 text-sm text-gray-600">
                                {selectedViewReview.timelinessRating}/4
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Review Images */}
                  {(selectedViewReview.imageNames?.length > 0 || selectedViewReview.imageUrls?.length > 0) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Review Images:</h4>
                      <ReviewImages
                        imageNames={selectedViewReview.imageNames}
                        imageUrls={selectedViewReview.imageUrls}
                        maxDisplay={10}
                        size="lg"
                        showCount={true}
                        folderName="review-images"
                        className="grid grid-cols-2 sm:grid-cols-3 gap-3"
                      />
                    </div>
                  )}

                  {/* Review Metadata */}
                  <div className="pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Submitted:</span>
                        <span className="ml-2">
                          {new Date(selectedViewReview.createdAt || selectedViewReview.date).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      {selectedViewReview.updatedAt && selectedViewReview.updatedAt !== (selectedViewReview.createdAt || selectedViewReview.date) && (
                        <div>
                          <span className="font-medium">Last Updated:</span>
                          <span className="ml-2">
                            {new Date(selectedViewReview.updatedAt).toLocaleString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                variant="light"
                onPress={handleCloseViewModal}
              >
                Close
              </Button>
              <Button
                color="primary"
                onPress={() => {
                  handleCloseViewModal();
                  handleEditReview(selectedViewReview);
                }}
              >
                Edit Review
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* Review Form Modal */}
      {showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => {
            setShowReviewForm(false);
            setSelectedReview(null);
            setIsEditMode(false);
          }}
          onSubmit={handleSubmitReview}
          providerId="default-provider"
          serviceId="default-service"
          bookingId="default-booking"
          serviceName="Service Review"
          initialData={
            isEditMode && selectedReview
              ? {
                  id: selectedReview.id || selectedReview._id,
                  providerId: selectedReview.providerId,
                  serviceId: selectedReview.serviceId,
                  serviceName: selectedReview.title,
                  rating: selectedReview.rating,
                  title: selectedReview.title,
                  review: selectedReview.review,
                  images: [],
                  imageUrls: selectedReview.imageUrls || [],
                  imageNames: selectedReview.imageNames || [], // Include image names
                  date: selectedReview.date,
                  serviceRating: selectedReview.serviceRating,
                  qualityRating: selectedReview.qualityRating,
                  valueRating: selectedReview.valueRating,
                  communicationRating: selectedReview.communicationRating,
                  timelinessRating: selectedReview.timelinessRating,
                }
              : undefined
          }
          isEdit={isEditMode}
        />
      )}
    </div>
  );
};

export default Reviews;
