/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';

// Review interfaces
export interface Review {
  id?: string;
  _id?: string; // MongoDB default ID field
  title: string;
  name?: string;
  email?: string;
  date: string;
  rating: number;
  review: string;
  comment?: string;
  images: string[];
  imageUrls: string[];
  imageNames?: string[]; // New field for storing image names
  profileImage?: string;
  serviceId?: string;
  providerId?: string;
  bookingId?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  userProfileImage?: string;
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
}

export interface CreateReviewData {
  providerId?: string;
  serviceId?: string;
  serviceName?: string;
  bookingId?: string;
  rating: number;
  title: string;
  review: string;
  comment?: string;
  images?: File[];
  imageUrls?: string[];
  imageNames?: string[]; // New field for storing image names
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  userName?: string;
  userEmail?: string;
  userProfileImage?: string;
  isVerified?: boolean;
  date?: string;
  createdAt?: string;
}

export interface UpdateReviewData {
  rating?: number;
  title?: string;
  review?: string;
  images?: File[];
  imageUrls?: string[];
  imageNames?: string[]; // New field for storing image names
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
}

export interface ReviewsResponse {
  reviews: Review[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  userId?: string;
}

// Utility types for image handling
export interface ImageUploadResult {
  imageName: string;
  fullUrl: string;
  key: string;
  originalName: string;
  size: number;
  timestamp: number;
}

export interface ImageDisplayData {
  imageNames: string[];
  imageUrls: string[];
}

// Type for review payload sent to backend
export interface ReviewPayload {
  providerId: string;
  serviceId: string;
  bookingId: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  imageNames: string[]; // Primary field for backend storage
  imageUrls?: string[]; // Optional field for compatibility
}

/**
 * Create a new review with image names storage
 */
export const createReview = async (data: CreateReviewData): Promise<Review> => {
  try {
    console.log('Creating review with data:', {
      ...data,
      imageCount: data.imageNames?.length || 0,
      hasImageNames: !!(data.imageNames && data.imageNames.length > 0)
    });

    // Validate required fields
    if (!data.providerId || data.providerId.trim() === '') {
      throw new Error('Provider ID is required and cannot be empty');
    }
    if (!data.serviceId || data.serviceId.trim() === '') {
      throw new Error('Service ID is required and cannot be empty');
    }
    if (!data.bookingId || data.bookingId.trim() === '') {
      throw new Error('Booking ID is required and cannot be empty');
    }
    if (!data.rating || data.rating < 1 || data.rating > 4) {
      throw new Error('Rating must be between 1 and 4');
    }
    if (!data.title || data.title.trim() === '') {
      throw new Error('Title is required and cannot be empty');
    }
    if (!data.review || data.review.trim() === '') {
      throw new Error('Review comment is required and cannot be empty');
    }
    if (data.review.trim().length < 10 || data.review.trim().length > 2000) {
      throw new Error('Review comment must be between 10 and 2000 characters');
    }

    // Prepare review payload with image names for backend storage
    const reviewPayload: any = {
      providerId: data.providerId.trim(),
      serviceId: data.serviceId.trim(),
      bookingId: data.bookingId.trim(),
      rating: Math.round(data.rating), // Ensure integer rating
      title: data.title.trim(),
      comment: (data.comment || data.review).trim(), // Backend expects comment field
      date: data.date || new Date().toISOString(),
      imageNames: data.imageNames || [], // Primary field: image names for database storage
      imageUrls: data.imageUrls || [] // Optional: for backward compatibility
    };

    // Only include rating fields that have valid values (1-4)
    if (data.serviceRating && data.serviceRating >= 1 && data.serviceRating <= 4) {
      reviewPayload.serviceRating = Math.round(data.serviceRating);
    }
    if (data.qualityRating && data.qualityRating >= 1 && data.qualityRating <= 4) {
      reviewPayload.qualityRating = Math.round(data.qualityRating);
    }
    if (data.valueRating && data.valueRating >= 1 && data.valueRating <= 4) {
      reviewPayload.valueRating = Math.round(data.valueRating);
    }
    if (data.communicationRating && data.communicationRating >= 1 && data.communicationRating <= 4) {
      reviewPayload.communicationRating = Math.round(data.communicationRating);
    }
    if (data.timelinessRating && data.timelinessRating >= 1 && data.timelinessRating <= 4) {
      reviewPayload.timelinessRating = Math.round(data.timelinessRating);
    }

    // Add user information if available
    if (data.userName) {
      reviewPayload.userName = data.userName.trim();
    }
    if (data.userEmail) {
      reviewPayload.userEmail = data.userEmail.trim();
    }
    if (data.userProfileImage) {
      reviewPayload.userProfileImage = data.userProfileImage.trim();
    }
    if (typeof data.isVerified === 'boolean') {
      reviewPayload.isVerified = data.isVerified;
    }

    console.log('Sending review payload to backend:', {
      ...reviewPayload,
      imageNamesCount: reviewPayload.imageNames.length
    });

    const response = await apiClient.post(Path.reviews, reviewPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Review created successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating review:', error);

    // Enhanced error logging for debugging
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    logger.error('Error creating review:', error);
    throw error;
  }
};

/**
 * Get all reviews with pagination
 */
export const getAllReviews = async (params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 3, userId } = params;
    console.log(`Fetching reviews - page: ${page}, limit: ${limit}, userId: ${userId}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      queryParams.append('userId', userId);
    }

    const response = await apiClient.get(`${Path.reviews}?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching reviews:', error);
    logger.error('Error fetching reviews:', error);
    throw error;
  }
};

/**
 * Get a specific review by ID
 */
export const getReviewById = async (reviewId: string): Promise<Review> => {
  try {
    console.log(`Fetching review with ID: ${reviewId}`);
    const response = await apiClient.get(`${Path.reviews}/${reviewId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching review ${reviewId}:`, error);
    logger.error(`Error fetching review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Update a review with image names support
 */
export const updateReview = async (reviewId: string, data: UpdateReviewData): Promise<Review> => {
  try {
    console.log(`Updating review ${reviewId} with data:`, {
      ...data,
      imageCount: data.imageNames?.length || 0,
      hasImageNames: !!(data.imageNames && data.imageNames.length > 0)
    });

    // Prepare update payload with image names
    const updatePayload = {
      ...data,
      imageNames: data.imageNames || [], // Ensure imageNames is always an array
      imageUrls: data.imageUrls // Keep for backward compatibility
    };

    console.log('Sending update payload to backend:', {
      reviewId,
      imageNamesCount: updatePayload.imageNames.length
    });

    const response = await apiClient.put(`${Path.reviews}/${reviewId}`, updatePayload);

    console.log('Review updated successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating review ${reviewId}:`, error);
    logger.error(`Error updating review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Delete a review
 */
export const deleteReview = async (reviewId: string): Promise<void> => {
  try {
    console.log(`Deleting review with ID: ${reviewId}`);
    await apiClient.delete(`${Path.reviews}/${reviewId}`);
  } catch (error: any) {
    console.error(`Error deleting review ${reviewId}:`, error);
    logger.error(`Error deleting review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by user ID
 */
export const getReviewsByUserId = async (userId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for user ${userId} - page: ${page}, limit: ${limit}`);
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      userId: userId,
    }).toString();
    
    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for user ${userId}:`, error);
    logger.error(`Error fetching reviews for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by service ID
 */
export const getReviewsByServiceId = async (serviceId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for service ${serviceId} - page: ${page}, limit: ${limit}`);
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      serviceId: serviceId,
    }).toString();
    
    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for service ${serviceId}:`, error);
    logger.error(`Error fetching reviews for service ${serviceId}:`, error);
    throw error;
  }
};
